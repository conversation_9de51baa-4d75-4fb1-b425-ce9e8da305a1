# AWS Credentials Setup

Este documento explica como configurar as credenciais AWS para o projeto audie-generator-ms.

## Para Produção (AWS Lambda) - RECOMENDADO

### 1. <PERSON>riar IAM Role para Lambda

1. No AWS Console, vá para **IAM > Roles**
2. Clique em **Create role**
3. Selecione **AWS service** > **Lambda**
4. <PERSON><PERSON>e as seguintes políticas:
   - `AWSLambdaBasicExecutionRole` (para logs do CloudWatch)
   - Política customizada para Secrets Manager:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
            ],
            "Resource": "arn do secret"
        }
    ]
}
```

5. <PERSON>meie a role (ex: `audie-generator-lambda-role`)

### 2. Configurar Lambda Function

1. No Lambda Console, selecione sua função
2. **Configuration > Permissions**
3. **Execution role > Edit**
4. Selecione a role criada (`audie-generator-lambda-role`)

### 3. Configurar Variáveis de Ambiente do Lambda (Opcional)

Se necessário, configure no Lambda Console:
- **Configuration > Environment variables**
- Adicione:
  ```
  AWS_SECRET_NAME=prod/audie
  AWS_REGION=us-east-1
  ```

### 4. Configurar Secret no AWS Secrets Manager

1. No AWS Console, vá para **Secrets Manager**
2. Clique em **Store a new secret**
3. Selecione **Other type of secret**
4. Adicione as chaves necessárias:
   ```json
   {
     "OPENAI_API_KEY": "sua-chave-openai",
     "VANNA_API_KEY": "sua-chave-vanna",
     "MYSQL_HOST": "seu-host-mysql",
     "MYSQL_DB": "seu-db",
     "MYSQL_USER": "seu-usuario",
     "MYSQL_PASSWORD": "sua-senha",
     "MYSQL_PORT": "3306",
     "DEEPSEEK_API_KEY": "sua-chave-deepseek",
     "MODEL_SQL": "seu-modelo"
   }
   ```
5. Nomeie o secret como `prod/audie`

## Para Desenvolvimento Local ou Testes

### Opção 1: AWS CLI (Recomendado)

1. Instale o AWS CLI:
   ```bash
   pip install awscli
   ```

2. Configure suas credenciais:
   ```bash
   aws configure
   ```
   
3. Insira:
   - AWS Access Key ID
   - AWS Secret Access Key
   - Default region: `us-east-1`
   - Default output format: `json`

### Opção 2: Variáveis de Ambiente

```bash
export AWS_ACCESS_KEY_ID=sua-access-key
export AWS_SECRET_ACCESS_KEY=sua-secret-key
export AWS_DEFAULT_REGION=us-east-1
```

### Opção 3: Arquivo de Credenciais

Crie o arquivo `~/.aws/credentials`:
```ini
[default]
aws_access_key_id = sua-access-key
aws_secret_access_key = sua-secret-key
```

E o arquivo `~/.aws/config`:
```ini
[default]
region = us-east-1
```

## Variáveis de Ambiente Opcionais

Você pode sobrescrever as configurações padrão:

```bash
export AWS_SECRET_NAME=prod/audie
export AWS_REGION=us-east-1
```

## Testando a Configuração

Para testar se as credenciais estão funcionando:

```python
import boto3

try:
    client = boto3.client('secretsmanager', region_name='us-east-1')
    response = client.get_secret_value(SecretId='prod/audie')
    print("✅ Credenciais AWS configuradas corretamente!")
except Exception as e:
    print(f"❌ Erro nas credenciais AWS: {e}")
```

## Segurança

### ✅ Boas Práticas:
- Use IAM Roles para Lambda (sem credenciais hardcoded)
- Princípio do menor privilégio nas políticas IAM
- Rotação regular de secrets
- Monitoramento de acesso via CloudTrail
- Use AWS Secrets Manager para dados sensíveis
- Configure timeout adequado no Lambda para operações de rede

### ❌ Evite:
- Credenciais no código fonte ou Dockerfile
- Credenciais em variáveis de ambiente em produção
- Políticas IAM muito permissivas
- Compartilhamento de credenciais
- Hardcoding de secrets na imagem Docker

## Troubleshooting

### Erro: "Unable to locate credentials"
- Verifique se a IAM role está anexada à função Lambda
- Para desenvolvimento local, configure AWS CLI ou credenciais
- Verifique se a função Lambda tem a role correta configurada

### Erro: "Access Denied"
- Verifique se a IAM role/usuário tem permissão para acessar o Secrets Manager
- Confirme o nome correto do secret
- Verifique se a política IAM está corretamente anexada à role do Lambda

### Erro: "Secret not found"
- Verifique se o secret `prod/audie` existe no Secrets Manager
- Confirme a região correta (us-east-1)
- Verifique se o nome do secret está correto no código

### Erro: "Lambda timeout"
- Aumente o timeout da função Lambda (Configuration > General configuration)
- Verifique conectividade de rede se usando VPC
- Otimize chamadas ao Secrets Manager (cache quando possível)

## Configurações Específicas do Lambda

### Timeout Recomendado
- **Mínimo**: 30 segundos
- **Recomendado**: 60-120 segundos (para operações com OpenAI/Vanna)

### Memory Configuration
- **Mínimo**: 512 MB
- **Recomendado**: 1024 MB (para melhor performance)

### Environment Variables (Opcionais)
```
AWS_SECRET_NAME=prod/audie
AWS_REGION=us-east-1
```

### Monitoramento
- Configure CloudWatch Logs para debugging
- Use X-Ray para tracing (opcional)
- Configure alarmes para timeouts e erros

## Migração de EC2 para Lambda

Se você está migrando de EC2 para Lambda:

1. **Remova credenciais hardcoded** do Dockerfile
2. **Configure IAM Role** específica para Lambda
3. **Teste localmente** com as mesmas credenciais
4. **Ajuste timeouts** conforme necessário
5. **Monitore logs** no CloudWatch após deploy
